<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock Tests - Placement Prep Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="index.html" class="text-xl font-semibold text-gray-800">Placement Prep Tracker</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Home
                        </a>
                        <a href="timetable.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Time Table
                        </a>
                        <a href="aptitude.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Aptitude
                        </a>
                        <a href="coding.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Coding
                        </a>
                        <a href="technical.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Technical
                        </a>
                        <a href="sql.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            SQL
                        </a>
                        <a href="mocks.html" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Mock Tests
                        </a>
                        <a href="resume.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Resume & Projects
                        </a>
                        <a href="profile.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white rounded-lg shadow px-5 py-6 sm:px-6">
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Mock Tests</h1>
                    <p class="text-gray-600">Track your mock test performance and schedule future tests</p>
                </div>

                <!-- Statistics -->
                <div class="mb-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="bg-purple-50 rounded-lg p-6 text-center">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Total Mocks</h3>
                        <div class="text-3xl font-bold text-purple-600" id="total-mocks">0</div>
                    </div>
                    <div class="bg-green-50 rounded-lg p-6 text-center">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Average Score</h3>
                        <div class="text-3xl font-bold text-green-600" id="average-score">0%</div>
                    </div>
                    <div class="bg-blue-50 rounded-lg p-6 text-center">
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">Best Score</h3>
                        <div class="text-3xl font-bold text-blue-600" id="best-score">0%</div>
                    </div>
                </div>

                <!-- Add New Mock Test -->
                <div class="mb-8 bg-gray-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Add New Mock Test</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                            <input type="date" id="mock-date" class="w-full border-gray-300 rounded-md">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                            <select id="mock-type" class="w-full border-gray-300 rounded-md">
                                <option value="Aptitude">Aptitude</option>
                                <option value="Coding">Coding</option>
                                <option value="Technical">Technical</option>
                                <option value="Full">Full Mock</option>
                                <option value="Interview">Interview</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Score (%)</label>
                            <input type="number" id="mock-score" min="0" max="100" class="w-full border-gray-300 rounded-md" placeholder="85">
                        </div>
                        <div class="flex items-end">
                            <button id="add-mock" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md">
                                Add Mock
                            </button>
                        </div>
                    </div>
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                        <textarea id="mock-notes" rows="2" class="w-full border-gray-300 rounded-md" placeholder="Performance notes, areas to improve..."></textarea>
                    </div>
                </div>
                <!-- Mock Tests List -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Mock Test History</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Score</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="mocks-table-body" class="bg-white divide-y divide-gray-200">
                                <!-- Mock tests will be inserted here -->
                            </tbody>
                        </table>
                        <div id="no-mocks" class="text-center py-8 text-gray-500" style="display: none;">
                            No mock tests recorded yet. Add your first mock test above!
                        </div>
                    </div>
                </div>

                <!-- Upcoming Mocks Calendar -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Schedule Future Mocks</h2>
                    <div class="bg-blue-50 rounded-lg p-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Scheduled Date</label>
                                <input type="date" id="scheduled-date" class="w-full border-gray-300 rounded-md">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Mock Type</label>
                                <select id="scheduled-type" class="w-full border-gray-300 rounded-md">
                                    <option value="Aptitude">Aptitude</option>
                                    <option value="Coding">Coding</option>
                                    <option value="Technical">Technical</option>
                                    <option value="Full">Full Mock</option>
                                    <option value="Interview">Interview</option>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button id="schedule-mock" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md">
                                    Schedule
                                </button>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Preparation Notes</label>
                            <textarea id="scheduled-notes" rows="2" class="w-full border-gray-300 rounded-md" placeholder="What to focus on for this mock..."></textarea>
                        </div>
                    </div>
                </div>

                <!-- Scheduled Mocks -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Upcoming Scheduled Mocks</h3>
                    <div id="scheduled-mocks" class="space-y-3">
                        <!-- Scheduled mocks will be inserted here -->
                    </div>
                    <div id="no-scheduled" class="text-center py-4 text-gray-500" style="display: none;">
                        No upcoming mocks scheduled.
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4">
                    <button id="export-mocks" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-md">
                        Export Data
                    </button>
                    <button id="reset-mocks" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-6 rounded-md">
                        Reset All
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 overflow-hidden sm:px-6 lg:px-8">
            <p class="mt-4 text-center text-base text-gray-500">
                &copy; 2025 Placement Prep Tracker. All rights reserved.
            </p>
        </div>
    </footer>

    <script src="js/app.js"></script>
    <script>
        // Mock tests page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            loadMocksData();
            setupMocksEventListeners();
            updateStatistics();
            displayMocks();
            displayScheduledMocks();
        });

        function setupMocksEventListeners() {
            // Add mock button
            document.getElementById('add-mock').addEventListener('click', addMock);

            // Schedule mock button
            document.getElementById('schedule-mock').addEventListener('click', scheduleMock);

            // Export button
            document.getElementById('export-mocks').addEventListener('click', exportMocks);

            // Reset button
            document.getElementById('reset-mocks').addEventListener('click', resetMocks);

            // Set today's date as default
            document.getElementById('mock-date').valueAsDate = new Date();
        }

        function addMock() {
            const date = document.getElementById('mock-date').value;
            const type = document.getElementById('mock-type').value;
            const score = document.getElementById('mock-score').value;
            const notes = document.getElementById('mock-notes').value;

            if (!date || !score) {
                alert('Please fill in date and score fields.');
                return;
            }

            const userData = PlacementPrepTracker.loadUserData();
            if (!userData.mocks) userData.mocks = [];

            const mock = {
                id: Date.now(),
                date: date,
                type: type,
                score: parseInt(score),
                notes: notes
            };

            userData.mocks.push(mock);
            PlacementPrepTracker.saveUserData(userData);

            // Clear form
            document.getElementById('mock-score').value = '';
            document.getElementById('mock-notes').value = '';
            document.getElementById('mock-date').valueAsDate = new Date();

            updateStatistics();
            displayMocks();
            alert('Mock test added successfully!');
        }

        function scheduleMock() {
            const date = document.getElementById('scheduled-date').value;
            const type = document.getElementById('scheduled-type').value;
            const notes = document.getElementById('scheduled-notes').value;

            if (!date) {
                alert('Please select a date.');
                return;
            }

            const userData = PlacementPrepTracker.loadUserData();
            if (!userData.scheduledMocks) userData.scheduledMocks = [];

            const scheduledMock = {
                id: Date.now(),
                date: date,
                type: type,
                notes: notes
            };

            userData.scheduledMocks.push(scheduledMock);
            PlacementPrepTracker.saveUserData(userData);

            // Clear form
            document.getElementById('scheduled-date').value = '';
            document.getElementById('scheduled-notes').value = '';

            displayScheduledMocks();
            alert('Mock test scheduled successfully!');
        }
        function loadMocksData() {
            const userData = PlacementPrepTracker.loadUserData();
            if (!userData.mocks) userData.mocks = [];
            if (!userData.scheduledMocks) userData.scheduledMocks = [];
        }

        function updateStatistics() {
            const userData = PlacementPrepTracker.loadUserData();
            const mocks = userData.mocks || [];

            const totalMocks = mocks.length;
            const averageScore = totalMocks > 0 ? Math.round(mocks.reduce((sum, mock) => sum + mock.score, 0) / totalMocks) : 0;
            const bestScore = totalMocks > 0 ? Math.max(...mocks.map(mock => mock.score)) : 0;

            document.getElementById('total-mocks').textContent = totalMocks;
            document.getElementById('average-score').textContent = averageScore + '%';
            document.getElementById('best-score').textContent = bestScore + '%';
        }

        function displayMocks() {
            const userData = PlacementPrepTracker.loadUserData();
            const mocks = userData.mocks || [];
            const tableBody = document.getElementById('mocks-table-body');
            const noMocks = document.getElementById('no-mocks');

            if (mocks.length === 0) {
                tableBody.innerHTML = '';
                noMocks.style.display = 'block';
                return;
            }

            noMocks.style.display = 'none';

            // Sort mocks by date (newest first)
            mocks.sort((a, b) => new Date(b.date) - new Date(a.date));

            tableBody.innerHTML = mocks.map(mock => `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${new Date(mock.date).toLocaleDateString()}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-${getTypeColor(mock.type)}-100 text-${getTypeColor(mock.type)}-800">
                            ${mock.type}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span class="font-semibold ${getScoreColor(mock.score)}">${mock.score}%</span>
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">${mock.notes || '-'}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="deleteMock(${mock.id})" class="text-red-600 hover:text-red-900">Delete</button>
                    </td>
                </tr>
            `).join('');
        }

        function displayScheduledMocks() {
            const userData = PlacementPrepTracker.loadUserData();
            const scheduledMocks = userData.scheduledMocks || [];
            const container = document.getElementById('scheduled-mocks');
            const noScheduled = document.getElementById('no-scheduled');

            if (scheduledMocks.length === 0) {
                container.innerHTML = '';
                noScheduled.style.display = 'block';
                return;
            }

            noScheduled.style.display = 'none';

            // Sort by date (earliest first)
            scheduledMocks.sort((a, b) => new Date(a.date) - new Date(b.date));

            container.innerHTML = scheduledMocks.map(mock => `
                <div class="bg-white border border-gray-200 rounded-lg p-4 flex justify-between items-center">
                    <div>
                        <div class="font-semibold text-gray-900">${new Date(mock.date).toLocaleDateString()} - ${mock.type}</div>
                        <div class="text-sm text-gray-500">${mock.notes || 'No notes'}</div>
                    </div>
                    <button onclick="deleteScheduledMock(${mock.id})" class="text-red-600 hover:text-red-900 text-sm">
                        Remove
                    </button>
                </div>
            `).join('');
        }

        function getTypeColor(type) {
            const colors = {
                'Aptitude': 'blue',
                'Coding': 'green',
                'Technical': 'yellow',
                'Full': 'purple',
                'Interview': 'indigo'
            };
            return colors[type] || 'gray';
        }

        function getScoreColor(score) {
            if (score >= 80) return 'text-green-600';
            if (score >= 60) return 'text-yellow-600';
            return 'text-red-600';
        }

        function deleteMock(id) {
            if (confirm('Are you sure you want to delete this mock test?')) {
                const userData = PlacementPrepTracker.loadUserData();
                userData.mocks = userData.mocks.filter(mock => mock.id !== id);
                PlacementPrepTracker.saveUserData(userData);
                updateStatistics();
                displayMocks();
            }
        }

        function deleteScheduledMock(id) {
            if (confirm('Are you sure you want to remove this scheduled mock?')) {
                const userData = PlacementPrepTracker.loadUserData();
                userData.scheduledMocks = userData.scheduledMocks.filter(mock => mock.id !== id);
                PlacementPrepTracker.saveUserData(userData);
                displayScheduledMocks();
            }
        }

        function exportMocks() {
            const userData = PlacementPrepTracker.loadUserData();
            const mocks = userData.mocks || [];

            if (mocks.length === 0) {
                alert('No mock tests to export.');
                return;
            }

            const csvContent = "data:text/csv;charset=utf-8,"
                + "Date,Type,Score,Notes\n"
                + mocks.map(mock => `${mock.date},${mock.type},${mock.score},"${mock.notes || ''}"`).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "mock_tests.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function resetMocks() {
            if (confirm('Are you sure you want to reset all mock test data? This cannot be undone.')) {
                const userData = PlacementPrepTracker.loadUserData();
                userData.mocks = [];
                userData.scheduledMocks = [];
                PlacementPrepTracker.saveUserData(userData);
                updateStatistics();
                displayMocks();
                displayScheduledMocks();
                alert('All mock test data has been reset.');
            }
        }
    </script>
</body>
</html>