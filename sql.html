<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL - Placement Prep Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="index.html" class="text-xl font-semibold text-gray-800">Placement Prep Tracker</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Home
                        </a>
                        <a href="timetable.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Time Table
                        </a>
                        <a href="aptitude.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Aptitude
                        </a>
                        <a href="coding.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Coding
                        </a>
                        <a href="technical.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Technical
                        </a>
                        <a href="sql.html" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            SQL
                        </a>
                        <a href="mocks.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Mock Tests
                        </a>
                        <a href="resume.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Resume & Projects
                        </a>
                        <a href="profile.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white rounded-lg shadow px-5 py-6 sm:px-6">
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">SQL Practice</h1>
                    <p class="text-gray-600">Master SQL concepts and queries for placement interviews</p>
                </div>

                <!-- Progress Overview -->
                <div class="mb-8 bg-indigo-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Overall Progress</h2>
                    <div class="w-full bg-gray-200 rounded-full h-4">
                        <div class="bg-indigo-600 h-4 rounded-full progress-bar" id="overall-progress" style="width: 0%"></div>
                    </div>
                    <div class="text-right text-sm text-gray-600 mt-2">
                        <span id="progress-text">0% complete</span>
                    </div>
                </div>

                <!-- SQL Topics -->
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">SQL Topics</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">SELECT</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="select">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="select"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_select.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools SELECT</a>
                                <a href="https://leetcode.com/studyplan/top-sql-50/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode SQL</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">INSERT</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="insert">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="insert"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_insert.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools INSERT</a>
                                <a href="https://sqlbolt.com/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">SQL Bolt</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">UPDATE</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="update">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="update"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_update.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools UPDATE</a>
                                <a href="https://www.hackerrank.com/domains/sql" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">HackerRank SQL</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">DELETE</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="delete">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="delete"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_delete.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools DELETE</a>
                                <a href="https://www.w3schools.com/sql/trysql.asp?filename=trysql_select_all" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">SQL Playground</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">WHERE</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="where">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="where"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_where.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools WHERE</a>
                                <a href="https://mode.com/sql-tutorial/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">Mode SQL Tutorial</a>
                            </div>
                        </div>
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">GROUP BY</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="group-by">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="group-by"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_groupby.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools GROUP BY</a>
                                <a href="https://www.codecademy.com/learn/learn-sql" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">Codecademy SQL</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">ORDER BY</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="order-by">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="order-by"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_orderby.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools ORDER BY</a>
                                <a href="https://sqlzoo.net/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">SQL Zoo</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">HAVING</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="having">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="having"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_having.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools HAVING</a>
                                <a href="https://www.datacamp.com/courses/intro-to-sql-for-data-science" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">DataCamp SQL</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">JOINS (Inner, Left, Right)</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="joins">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="joins"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_join.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools JOINS</a>
                                <a href="https://joins.spathon.com/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">Visual JOIN Tool</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Subqueries</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="subqueries">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="subqueries"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_subqueries.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools Subqueries</a>
                                <a href="https://www.sqltutorial.org/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">SQL Tutorial</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Aggregate Functions</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="aggregate">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="aggregate"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_count_avg_sum.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools Aggregate</a>
                                <a href="https://www.postgresql.org/docs/current/functions-aggregate.html" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">PostgreSQL Docs</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Constraints</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="constraints">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="constraints"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_constraints.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools Constraints</a>
                                <a href="https://www.mysqltutorial.org/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">MySQL Tutorial</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Views</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="views">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="views"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_view.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools Views</a>
                                <a href="https://www.db-fiddle.com/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">DB Fiddle</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Indexes</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="indexes">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="indexes"></textarea>
                            <div class="space-y-1">
                                <a href="https://www.w3schools.com/sql/sql_create_index.asp" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">W3Schools Indexes</a>
                                <a href="https://use-the-index-luke.com/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">Use The Index</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4">
                    <button id="save-progress" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-6 rounded-md">
                        Save Progress
                    </button>
                    <button id="reset-sql" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-6 rounded-md">
                        Reset All
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 overflow-hidden sm:px-6 lg:px-8">
            <p class="mt-4 text-center text-base text-gray-500">
                &copy; 2025 Placement Prep Tracker. All rights reserved.
            </p>
        </div>
    </footer>

    <script src="js/app.js"></script>
    <script>
        // SQL page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            loadSQLData();
            setupSQLEventListeners();
            updateProgress();
        });

        function setupSQLEventListeners() {
            // Topic checkboxes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    saveSQLData();
                    updateProgress();
                });
            });

            // Notes textareas
            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                textarea.addEventListener('blur', saveSQLData);
            });

            // Save button
            document.getElementById('save-progress').addEventListener('click', function() {
                saveSQLData();
                alert('Progress saved successfully!');
            });

            // Reset button
            document.getElementById('reset-sql').addEventListener('click', resetSQLData);
        }

        function loadSQLData() {
            const userData = PlacementPrepTracker.loadUserData();
            const sqlData = userData.sql || {};

            // Load checkboxes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                const topic = checkbox.dataset.topic;
                checkbox.checked = sqlData[topic]?.completed || false;
            });

            // Load notes
            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                const topic = textarea.dataset.notes;
                textarea.value = sqlData[topic]?.notes || '';
            });
        }

        function saveSQLData() {
            const userData = PlacementPrepTracker.loadUserData();
            if (!userData.sql) userData.sql = {};

            // Save checkboxes and notes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                const topic = checkbox.dataset.topic;
                if (!userData.sql[topic]) userData.sql[topic] = {};
                userData.sql[topic].completed = checkbox.checked;
            });

            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                const topic = textarea.dataset.notes;
                if (!userData.sql[topic]) userData.sql[topic] = {};
                userData.sql[topic].notes = textarea.value;
            });

            PlacementPrepTracker.saveUserData(userData);
        }

        function updateProgress() {
            const totalTopics = document.querySelectorAll('.topic-checkbox').length;
            const completedTopics = document.querySelectorAll('.topic-checkbox:checked').length;
            const progressPercentage = Math.round((completedTopics / totalTopics) * 100);

            document.getElementById('overall-progress').style.width = progressPercentage + '%';
            document.getElementById('progress-text').textContent = progressPercentage + '% complete';
        }

        function resetSQLData() {
            if (confirm('Are you sure you want to reset all SQL progress? This cannot be undone.')) {
                const userData = PlacementPrepTracker.loadUserData();
                userData.sql = {};
                PlacementPrepTracker.saveUserData(userData);
                loadSQLData();
                updateProgress();
                alert('SQL progress has been reset.');
            }
        }
    </script>
</body>
</html>