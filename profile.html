<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Placement Prep Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="index.html" class="text-xl font-semibold text-gray-800">Placement Prep Tracker</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8
                    ">
                        <a href="index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Home
                        </a>
                        <a href="timetable.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Time Table
                        </a>
                        <a href="aptitude.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Aptitude
                        </a>
                        <a href="coding.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Coding
                        </a>
                        <a href="technical.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Technical
                        </a>
                        <a href="sql.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            SQL
                        </a>
                        <a href="mocks.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Mock Tests
                        </a>
                        <a href="resume.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Resume & Projects
                        </a>
                        <a href="profile.html" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Profile
                        </a>
                    </div>
                </div>
                <!-- Mobile menu button -->
                <div class="-mr-2 flex items-center sm:hidden">
                    <button type="button" id="mobile-menu-button" class="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500">
                        <span class="sr-only">Open main menu</span>
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile menu, show/hide based on menu state -->
        <div class="sm:hidden hidden" id="mobile-menu">
            <div class="pt-2 pb-3 space-y-1">
                <a href="index.html" class="bg-indigo-50 border-indigo-500 text-indigo-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Home</a>
                <a href="timetable.html" class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Time Table</a>
                <a href="aptitude.html" class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Aptitude</a>
                <a href="coding.html" class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Coding</a>
                <a href="technical.html" class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Technical</a>
                <a href="sql.html" class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">SQL</a>
                <a href="mocks.html" class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Mock Tests</a>
                <a href="resume.html" class="border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Resume & Projects</a>
                <a href="profile.html" class="bg-indigo-50 border-indigo-500 text-indigo-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Profile</a>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white shadow overflow-hidden sm:rounded-lg">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                        Profile Information
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">
                        Update your personal details and placement information.
                    </p>
                </div>
                <div class="border-t border-gray-200">
                    <form id="profile-form" class="p-6">
                        <div class="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                            <div class="sm:col-span-3">
                                <label for="name" class="block text-sm font-medium text-gray-700">Full name</label>
                                <div class="mt-1">
                                    <input type="text" name="name" id="name" autocomplete="name" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>

                            <div class="sm:col-span-4">
                                <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
                                <div class="mt-1">
                                    <input id="email" name="email" type="email" autocomplete="email" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                            </div>

                            <div class="sm:col-span-3">
                                <label for="placement-date" class="block text-sm font-medium text-gray-700">Placement Start Date</label>
                                <div class="mt-1">
                                    <input type="date" name="placement-date" id="placement-date" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
                                </div>
                                <p class="mt-2 text-sm text-gray-500" id="days-remaining-text"></p>
                            </div>

                            <div class="sm:col-span-6">
                                <label for="target-companies" class="block text-sm font-medium text-gray-700">Target Companies</label>
                                <div class="mt-1">
                                    <textarea id="target-companies" name="target-companies" rows="3" class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md" placeholder="e.g., TCS, Infosys, Wipro, Accenture, Cognizant"></textarea>
                                </div>
                                <p class="mt-2 text-sm text-gray-500">Enter company names separated by commas.</p>
                            </div>
                        </div>

                        <div class="mt-8 flex">
                            <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Save Changes
                            </button>
                            <button type="button" id="reset-btn" class="ml-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                Reset
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="bg-white shadow overflow-hidden sm:rounded-lg mt-8">
                <div class="px-4 py-5 sm:px-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">
                        Progress Overview
                    </h3>
                    <p class="mt-1 max-w-2xl text-sm text-gray-500">
                        Your current preparation progress across different areas.
                    </p>
                </div>
                <div class="border-t border-gray-200 px-4 py-5 sm:p-0">
                    <dl class="sm:divide-y sm:divide-gray-200">
                        <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Aptitude</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-blue-600 h-2.5 rounded-full progress-aptitude" style="width: 0%"></div>
                                </div>
                                <div class="text-right text-sm text-gray-500 mt-1">
                                    <span class="percentage-aptitude">0</span>% complete
                                </div>
                            </dd>
                        </div>
                        <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Coding</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-green-600 h-2.5 rounded-full progress-coding" style="width: 0%"></div>
                                </div>
                                <div class="text-right text-sm text-gray-500 mt-1">
                                    <span class="percentage-coding">0</span>% complete
                                </div>
                            </dd>
                        </div>
                        <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Technical</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <div class="w-full bg-gray-200 rounded-full h-2.5">
                                    <div class="bg-yellow-600 h-2.5 rounded-full progress-technical" style="width: 0%"></div>
                                </div>
                                <div class="text-right text-sm text-gray-500 mt-1">
                                    <span class="percentage-technical">0</span>% complete
                                </div>
                            </dd>
                        </div>
                        <div class="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                            <dt class="text-sm font-medium text-gray-500">Mock Tests Completed</dt>
                            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                                <div class="flex items-center">
                                    <span class="text-lg font-semibold text-purple-600" data-mocks-count>0</span>
                                    <span class="ml-2 text-sm text-gray-500">tests completed</span>
                                </div>
                            </dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 overflow-hidden sm:px-6 lg:px-8">
            <p class="mt-4 text-center text-base text-gray-500">
                &copy; 2025 Placement Prep Tracker. All rights reserved.
            </p>
        </div>
    </footer>

    <script src="js/app.js"></script>
    <script>
        // Profile page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Load saved profile data
            loadProfileData();
            
            // Set up form submission
            const profileForm = document.getElementById('profile-form');
            if (profileForm) {
                profileForm.addEventListener('submit', saveProfileData);
            }
            
            // Set up reset button
            const resetBtn = document.getElementById('reset-btn');
            if (resetBtn) {
                resetBtn.addEventListener('click', resetProfileData);
            }
            
            // Update days remaining when date changes
            const placementDateInput = document.getElementById('placement-date');
            if (placementDateInput) {
                placementDateInput.addEventListener('change', updateDaysRemaining);
            }
            
            // Initial UI update
            updateUI();
        });
        
        // Load profile data from localStorage
        function loadProfileData() {
            const userData = PlacementPrepTracker.loadUserData();
            
            // Set form values
            document.getElementById('name').value = userData.profile.name || '';
            document.getElementById('email').value = userData.profile.email || '';
            
            // Format date for input field (YYYY-MM-DD)
            if (userData.profile.placementStartDate) {
                const date = new Date(userData.profile.placementStartDate);
                document.getElementById('placement-date').value = PlacementPrepTracker.formatDate(date);
            }
            
            // Set target companies
            if (userData.profile.targetCompanies && userData.profile.targetCompanies.length > 0) {
                document.getElementById('target-companies').value = userData.profile.targetCompanies.join(', ');
            }
            
            // Update days remaining display
            updateDaysRemaining();
        }
        
        // Save profile data to localStorage
        function saveProfileData(e) {
            e.preventDefault();
            
            const userData = PlacementPrepTracker.loadUserData();
            
            // Update profile data
            userData.profile = {
                name: document.getElementById('name').value.trim(),
                email: document.getElementById('email').value.trim(),
                placementStartDate: document.getElementById('placement-date').value,
                targetCompanies: document.getElementById('target-companies').value
                    .split(',')
                    .map(company => company.trim())
                    .filter(company => company.length > 0)
            };
            
            // Save to localStorage
            PlacementPrepTracker.saveUserData(userData);
            
            // Show success message
            alert('Profile saved successfully!');
            
            // Update UI
            updateUI();
        }
        
        // Reset profile data
        function resetProfileData() {
            if (confirm('Are you sure you want to reset all profile data? This cannot be undone.')) {
                // Clear only profile data, keep progress
                const userData = PlacementPrepTracker.loadUserData();
                userData.profile = {
                    name: '',
                    email: '',
                    placementStartDate: '',
                    targetCompanies: []
                };
                
                // Save and reload
                PlacementPrepTracker.saveUserData(userData);
                loadProfileData();
                
                // Update UI
                updateUI();
                
                alert('Profile data has been reset.');
            }
        }
        
        // Update days remaining display
        function updateDaysRemaining() {
            const daysRemainingEl = document.getElementById('days-remaining-text');
            if (!daysRemainingEl) return;
            
            const placementDate = document.getElementById('placement-date').value;
            
            if (placementDate) {
                const today = new Date();
                const placement = new Date(placementDate);
                
                // Reset time part to avoid timezone issues
                today.setHours(0, 0, 0, 0);
                placement.setHours(0, 0, 0, 0);
                
                // Calculate difference in days
                const diffTime = placement - today;
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                
                if (diffDays > 0) {
                    daysRemainingEl.textContent = `${diffDays} days remaining until placements start.`;
                    daysRemainingEl.className = 'mt-2 text-sm text-green-600 font-medium';
                } else if (diffDays === 0) {
                    daysRemainingEl.textContent = 'Placements start today! Good luck!';
                    daysRemainingEl.className = 'mt-2 text-sm text-blue-600 font-medium';
                } else {
                    daysRemainingEl.textContent = `Placements started ${Math.abs(diffDays)} days ago.`;
                    daysRemainingEl.className = 'mt-2 text-sm text-indigo-600 font-medium';
                }
            } else {
                daysRemainingEl.textContent = 'Please select your placement start date to see days remaining.';
                daysRemainingEl.className = 'mt-2 text-sm text-gray-500';
            }
        }
        
        // Update UI elements
        function updateUI() {
            // Update days remaining in the profile page
            updateDaysRemaining();
            
            // Update progress bars from the main app
            PlacementPrepTracker.updateUI();
        }
    </script>
</body>
</html>
