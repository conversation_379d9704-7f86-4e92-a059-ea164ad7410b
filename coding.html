<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coding - Placement Prep Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="index.html" class="text-xl font-semibold text-gray-800">Placement Prep Tracker</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Home
                        </a>
                        <a href="timetable.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Time Table
                        </a>
                        <a href="aptitude.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Aptitude
                        </a>
                        <a href="coding.html" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Coding
                        </a>
                        <a href="technical.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Technical
                        </a>
                        <a href="sql.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            SQL
                        </a>
                        <a href="mocks.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Mock Tests
                        </a>
                        <a href="resume.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Resume & Projects
                        </a>
                        <a href="profile.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white rounded-lg shadow px-5 py-6 sm:px-6">
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">DSA & Coding Practice</h1>
                    <p class="text-gray-600">Track your progress in Data Structures and Algorithms</p>
                </div>

                <!-- Progress Overview -->
                <div class="mb-8 bg-green-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Overall Progress</h2>
                    <div class="w-full bg-gray-200 rounded-full h-4">
                        <div class="bg-green-600 h-4 rounded-full progress-bar" id="overall-progress" style="width: 0%"></div>
                    </div>
                    <div class="text-right text-sm text-gray-600 mt-2">
                        <span id="progress-text">0% complete</span>
                    </div>
                </div>

                <!-- DSA Topics -->
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">DSA Topics</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Arrays</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="arrays">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="arrays"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/array/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Arrays</a>
                                <a href="https://www.geeksforgeeks.org/array-data-structure/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Arrays</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Strings</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="strings">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="strings"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/string/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Strings</a>
                                <a href="https://www.geeksforgeeks.org/string-data-structure/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Strings</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Sorting</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="sorting">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="sorting"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/sorting/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Sorting</a>
                                <a href="https://www.geeksforgeeks.org/sorting-algorithms/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Sorting</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Hashing</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="hashing">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="hashing"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/hash-table/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Hashing</a>
                                <a href="https://www.geeksforgeeks.org/hashing-data-structure/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Hashing</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Stack</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="stack">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="stack"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/stack/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Stack</a>
                                <a href="https://www.geeksforgeeks.org/stack-data-structure/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Stack</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Queue</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="queue">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="queue"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/queue/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Queue</a>
                                <a href="https://www.geeksforgeeks.org/queue-data-structure/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Queue</a>
                            </div>
                        </div>
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">LinkedList</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="linkedlist">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="linkedlist"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/linked-list/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode LinkedList</a>
                                <a href="https://www.geeksforgeeks.org/data-structures/linked-list/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG LinkedList</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Recursion</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="recursion">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="recursion"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/recursion/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Recursion</a>
                                <a href="https://www.geeksforgeeks.org/recursion/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Recursion</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Trees</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="trees">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="trees"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/tree/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Trees</a>
                                <a href="https://www.geeksforgeeks.org/binary-tree-data-structure/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Trees</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Binary Search</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="binary-search">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="binary-search"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/binary-search/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Binary Search</a>
                                <a href="https://www.geeksforgeeks.org/binary-search/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Binary Search</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Sliding Window</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="sliding-window">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="sliding-window"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/sliding-window/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Sliding Window</a>
                                <a href="https://www.geeksforgeeks.org/window-sliding-technique/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Sliding Window</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Graphs</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="graphs">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="graphs"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/graph/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode Graphs</a>
                                <a href="https://www.geeksforgeeks.org/graph-data-structure-and-algorithms/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG Graphs</a>
                            </div>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Dynamic Programming</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="dp">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md mb-3" rows="2" placeholder="Notes..." data-notes="dp"></textarea>
                            <div class="space-y-1">
                                <a href="https://leetcode.com/tag/dynamic-programming/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">LeetCode DP</a>
                                <a href="https://www.geeksforgeeks.org/dynamic-programming/" target="_blank" class="text-xs text-blue-600 hover:text-blue-800 block">GFG DP</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4">
                    <button id="save-progress" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-md">
                        Save Progress
                    </button>
                    <button id="reset-coding" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-6 rounded-md">
                        Reset All
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 overflow-hidden sm:px-6 lg:px-8">
            <p class="mt-4 text-center text-base text-gray-500">
                &copy; 2025 Placement Prep Tracker. All rights reserved.
            </p>
        </div>
    </footer>

    <script src="js/app.js"></script>
    <script>
        // Coding page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            loadCodingData();
            setupCodingEventListeners();
            updateProgress();
        });

        function setupCodingEventListeners() {
            // Topic checkboxes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    saveCodingData();
                    updateProgress();
                });
            });

            // Notes textareas
            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                textarea.addEventListener('blur', saveCodingData);
            });

            // Save button
            document.getElementById('save-progress').addEventListener('click', function() {
                saveCodingData();
                alert('Progress saved successfully!');
            });

            // Reset button
            document.getElementById('reset-coding').addEventListener('click', resetCodingData);
        }

        function loadCodingData() {
            const userData = PlacementPrepTracker.loadUserData();
            const codingData = userData.coding || {};

            // Load checkboxes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                const topic = checkbox.dataset.topic;
                checkbox.checked = codingData[topic]?.completed || false;
            });

            // Load notes
            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                const topic = textarea.dataset.notes;
                textarea.value = codingData[topic]?.notes || '';
            });
        }

        function saveCodingData() {
            const userData = PlacementPrepTracker.loadUserData();
            if (!userData.coding) userData.coding = {};

            // Save checkboxes and notes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                const topic = checkbox.dataset.topic;
                if (!userData.coding[topic]) userData.coding[topic] = {};
                userData.coding[topic].completed = checkbox.checked;
            });

            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                const topic = textarea.dataset.notes;
                if (!userData.coding[topic]) userData.coding[topic] = {};
                userData.coding[topic].notes = textarea.value;
            });

            // Calculate and save overall progress
            const totalTopics = document.querySelectorAll('.topic-checkbox').length;
            const completedTopics = document.querySelectorAll('.topic-checkbox:checked').length;
            const progressPercentage = Math.round((completedTopics / totalTopics) * 100);

            userData.progress.coding = progressPercentage;

            PlacementPrepTracker.saveUserData(userData);
        }

        function updateProgress() {
            const totalTopics = document.querySelectorAll('.topic-checkbox').length;
            const completedTopics = document.querySelectorAll('.topic-checkbox:checked').length;
            const progressPercentage = Math.round((completedTopics / totalTopics) * 100);

            document.getElementById('overall-progress').style.width = progressPercentage + '%';
            document.getElementById('progress-text').textContent = progressPercentage + '% complete';
        }

        function resetCodingData() {
            if (confirm('Are you sure you want to reset all coding progress? This cannot be undone.')) {
                const userData = PlacementPrepTracker.loadUserData();
                userData.coding = {};
                userData.progress.coding = 0;
                PlacementPrepTracker.saveUserData(userData);
                loadCodingData();
                updateProgress();
                alert('Coding progress has been reset.');
            }
        }
    </script>
</body>
</html>