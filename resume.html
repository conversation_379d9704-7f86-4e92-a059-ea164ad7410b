<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume & Projects - Placement Prep Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="index.html" class="text-xl font-semibold text-gray-800">Placement Prep Tracker</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Home
                        </a>
                        <a href="timetable.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Time Table
                        </a>
                        <a href="aptitude.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Aptitude
                        </a>
                        <a href="coding.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Coding
                        </a>
                        <a href="technical.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Technical
                        </a>
                        <a href="sql.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            SQL
                        </a>
                        <a href="mocks.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Mock Tests
                        </a>
                        <a href="resume.html" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Resume & Projects
                        </a>
                        <a href="profile.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Profile
                        </a>
                    </div>
                </div>
                <div class="sm:hidden">
                    <!-- Mobile menu button -->
                    <button type="button" class="bg-white inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500" aria-controls="mobile-menu" aria-expanded="false" id="mobile-menu-button">
                        <span class="sr-only">Open main menu</span>
                        <!-- Hamburger icon -->
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                        <!-- Close icon (hidden by default) -->
                        <svg class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="sm:hidden hidden" id="mobile-menu">
            <div class="pt-2 pb-3 space-y-1">
                <a href="index.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Home</a>
                <a href="timetable.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Time Table</a>
                <a href="aptitude.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Aptitude</a>
                <a href="coding.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Coding</a>
                <a href="technical.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Technical</a>
                <a href="sql.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">SQL</a>
                <a href="mocks.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Mock Tests</a>
                <a href="resume.html" class="bg-indigo-50 border-indigo-500 text-indigo-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Resume & Projects</a>
                <a href="profile.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Profile</a>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white rounded-lg shadow px-5 py-6 sm:px-6">
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Resume & Projects</h1>
                    <p class="text-gray-600">Manage your projects and resume checklist for placements</p>
                </div>

                <!-- Resume Checklist -->
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">Resume Checklist</h2>
                    <div class="bg-blue-50 rounded-lg p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="flex items-center">
                                <input type="checkbox" id="ats-format" class="resume-checkbox mr-3 h-5 w-5 text-blue-600">
                                <label for="ats-format" class="text-sm font-medium text-gray-700">ATS Format used</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="skills-added" class="resume-checkbox mr-3 h-5 w-5 text-blue-600">
                                <label for="skills-added" class="text-sm font-medium text-gray-700">Skills added</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="projects-listed" class="resume-checkbox mr-3 h-5 w-5 text-blue-600">
                                <label for="projects-listed" class="text-sm font-medium text-gray-700">Projects listed</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="final-reviewed" class="resume-checkbox mr-3 h-5 w-5 text-blue-600">
                                <label for="final-reviewed" class="text-sm font-medium text-gray-700">Final reviewed</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="contact-updated" class="resume-checkbox mr-3 h-5 w-5 text-blue-600">
                                <label for="contact-updated" class="text-sm font-medium text-gray-700">Contact info updated</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="education-added" class="resume-checkbox mr-3 h-5 w-5 text-blue-600">
                                <label for="education-added" class="text-sm font-medium text-gray-700">Education details added</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="experience-added" class="resume-checkbox mr-3 h-5 w-5 text-blue-600">
                                <label for="experience-added" class="text-sm font-medium text-gray-700">Experience/Internships added</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="achievements-added" class="resume-checkbox mr-3 h-5 w-5 text-blue-600">
                                <label for="achievements-added" class="text-sm font-medium text-gray-700">Achievements added</label>
                            </div>
                        </div>
                        <div class="mt-6">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Resume Completion</span>
                                <span class="text-sm font-medium text-blue-600" id="resume-percentage">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" id="resume-progress" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Add New Project -->
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">Project Tracker</h2>
                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Add New Project</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Project Title</label>
                                <input type="text" id="project-title" class="w-full border-gray-300 rounded-md" placeholder="E-commerce Website">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tech Stack</label>
                                <input type="text" id="project-tech" class="w-full border-gray-300 rounded-md" placeholder="React, Node.js, MongoDB">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">GitHub Link</label>
                                <input type="url" id="project-github" class="w-full border-gray-300 rounded-md" placeholder="https://github.com/username/project">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select id="project-status" class="w-full border-gray-300 rounded-md">
                                    <option value="In Progress">In Progress</option>
                                    <option value="Completed">Completed</option>
                                    <option value="On Hold">On Hold</option>
                                    <option value="Planning">Planning</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Short Description</label>
                            <textarea id="project-description" rows="3" class="w-full border-gray-300 rounded-md" placeholder="Brief description of the project, key features, and your role..."></textarea>
                        </div>
                        <div class="mt-4">
                            <button id="add-project" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-md">
                                Add Project
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Projects List -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Your Projects</h3>
                    <div id="projects-container" class="space-y-4">
                        <!-- Projects will be inserted here -->
                    </div>
                    <div id="no-projects" class="text-center py-8 text-gray-500" style="display: none;">
                        No projects added yet. Add your first project above!
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4">
                    <button id="save-resume-data" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md">
                        Save Progress
                    </button>
                    <button id="export-projects" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-md">
                        Export Projects
                    </button>
                    <button id="reset-resume" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-6 rounded-md">
                        Reset All
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 overflow-hidden sm:px-6 lg:px-8">
            <p class="mt-4 text-center text-base text-gray-500">
                &copy; 2025 Placement Prep Tracker. All rights reserved.
            </p>
        </div>
    </footer>

    <script src="js/app.js"></script>
    <script>
        // Resume & Projects page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            loadResumeData();
            setupResumeEventListeners();
            updateResumeProgress();
            displayProjects();
        });

        function setupResumeEventListeners() {
            // Resume checkboxes
            document.querySelectorAll('.resume-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    saveResumeData();
                    updateResumeProgress();
                });
            });

            // Add project button
            document.getElementById('add-project').addEventListener('click', addProject);

            // Save button
            document.getElementById('save-resume-data').addEventListener('click', function() {
                saveResumeData();
                alert('Progress saved successfully!');
            });

            // Export button
            document.getElementById('export-projects').addEventListener('click', exportProjects);

            // Reset button
            document.getElementById('reset-resume').addEventListener('click', resetResumeData);
        }

        function loadResumeData() {
            const userData = PlacementPrepTracker.loadUserData();
            const resumeData = userData.resume || {};

            // Load resume checklist
            document.querySelectorAll('.resume-checkbox').forEach(checkbox => {
                checkbox.checked = resumeData[checkbox.id] || false;
            });

            // Initialize projects array if it doesn't exist
            if (!userData.projects) userData.projects = [];
        }

        function saveResumeData() {
            const userData = PlacementPrepTracker.loadUserData();
            if (!userData.resume) userData.resume = {};

            // Save resume checklist
            document.querySelectorAll('.resume-checkbox').forEach(checkbox => {
                userData.resume[checkbox.id] = checkbox.checked;
            });

            PlacementPrepTracker.saveUserData(userData);
        }

        function updateResumeProgress() {
            const totalItems = document.querySelectorAll('.resume-checkbox').length;
            const completedItems = document.querySelectorAll('.resume-checkbox:checked').length;
            const percentage = Math.round((completedItems / totalItems) * 100);

            document.getElementById('resume-percentage').textContent = percentage + '%';
            document.getElementById('resume-progress').style.width = percentage + '%';
        }

        function addProject() {
            const title = document.getElementById('project-title').value.trim();
            const tech = document.getElementById('project-tech').value.trim();
            const github = document.getElementById('project-github').value.trim();
            const status = document.getElementById('project-status').value;
            const description = document.getElementById('project-description').value.trim();

            if (!title || !tech || !description) {
                alert('Please fill in title, tech stack, and description fields.');
                return;
            }

            const userData = PlacementPrepTracker.loadUserData();
            if (!userData.projects) userData.projects = [];

            const project = {
                id: Date.now(),
                title: title,
                tech: tech,
                github: github,
                status: status,
                description: description,
                dateAdded: new Date().toISOString().split('T')[0]
            };

            userData.projects.push(project);
            PlacementPrepTracker.saveUserData(userData);

            // Clear form
            document.getElementById('project-title').value = '';
            document.getElementById('project-tech').value = '';
            document.getElementById('project-github').value = '';
            document.getElementById('project-description').value = '';
            document.getElementById('project-status').value = 'In Progress';

            displayProjects();
            alert('Project added successfully!');
        }
        function displayProjects() {
            const userData = PlacementPrepTracker.loadUserData();
            const projects = userData.projects || [];
            const container = document.getElementById('projects-container');
            const noProjects = document.getElementById('no-projects');

            if (projects.length === 0) {
                container.innerHTML = '';
                noProjects.style.display = 'block';
                return;
            }

            noProjects.style.display = 'none';

            container.innerHTML = projects.map(project => `
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900">${project.title}</h4>
                            <p class="text-sm text-gray-500">Added on ${new Date(project.dateAdded).toLocaleDateString()}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(project.status)}">
                                ${project.status}
                            </span>
                            <button onclick="deleteProject(${project.id})" class="text-red-600 hover:text-red-900 text-sm">
                                Delete
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <span class="text-sm font-medium text-gray-700">Tech Stack: </span>
                        <span class="text-sm text-gray-600">${project.tech}</span>
                    </div>
                    <p class="text-sm text-gray-600 mb-3">${project.description}</p>
                    ${project.github ? `
                        <a href="${project.github}" target="_blank" class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                            <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd"></path>
                            </svg>
                            View on GitHub
                        </a>
                    ` : ''}
                </div>
            `).join('');
        }

        function getStatusColor(status) {
            const colors = {
                'Completed': 'bg-green-100 text-green-800',
                'In Progress': 'bg-blue-100 text-blue-800',
                'On Hold': 'bg-yellow-100 text-yellow-800',
                'Planning': 'bg-gray-100 text-gray-800'
            };
            return colors[status] || 'bg-gray-100 text-gray-800';
        }

        function deleteProject(id) {
            if (confirm('Are you sure you want to delete this project?')) {
                const userData = PlacementPrepTracker.loadUserData();
                userData.projects = userData.projects.filter(project => project.id !== id);
                PlacementPrepTracker.saveUserData(userData);
                displayProjects();
            }
        }

        function exportProjects() {
            const userData = PlacementPrepTracker.loadUserData();
            const projects = userData.projects || [];

            if (projects.length === 0) {
                alert('No projects to export.');
                return;
            }

            const csvContent = "data:text/csv;charset=utf-8,"
                + "Title,Tech Stack,Status,GitHub,Description,Date Added\n"
                + projects.map(project => `"${project.title}","${project.tech}","${project.status}","${project.github || ''}","${project.description}","${project.dateAdded}"`).join("\n");

            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "projects.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        function resetResumeData() {
            if (confirm('Are you sure you want to reset all resume and project data? This cannot be undone.')) {
                const userData = PlacementPrepTracker.loadUserData();
                userData.resume = {};
                userData.projects = [];
                PlacementPrepTracker.saveUserData(userData);
                loadResumeData();
                updateResumeProgress();
                displayProjects();
                alert('All resume and project data has been reset.');
            }
        }
    </script>
</body>
</html>