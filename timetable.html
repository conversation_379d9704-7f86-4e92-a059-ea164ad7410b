<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Time Table - Placement Prep Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="index.html" class="text-xl font-semibold text-gray-800">Placement Prep Tracker</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Home
                        </a>
                        <a href="timetable.html" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Time Table
                        </a>
                        <a href="aptitude.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Aptitude
                        </a>
                        <a href="coding.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Coding
                        </a>
                        <a href="technical.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Technical
                        </a>
                        <a href="sql.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            SQL
                        </a>
                        <a href="mocks.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Mock Tests
                        </a>
                        <a href="resume.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Resume & Projects
                        </a>
                        <a href="profile.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white rounded-lg shadow px-5 py-6 sm:px-6">
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Daily Time Table</h1>
                    <p class="text-gray-600">Track your daily preparation schedule</p>
                </div>

                <!-- Weekday Schedule -->
                <div class="mb-8">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Monday - Friday Schedule</h2>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mon</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tue</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Wed</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thu</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fri</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">7:00–8:30 AM</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Aptitude Practice (alternate Quants / Logical)</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="aptitude-mon"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="aptitude-tue"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="aptitude-wed"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="aptitude-thu"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="aptitude-fri"></td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">9:00–11:00 AM</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">DSA Topic Practice</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="dsa-mon"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="dsa-tue"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="dsa-wed"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="dsa-thu"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="dsa-fri"></td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">11:30–1:00 PM</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Company-Specific Coding Practice</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="coding-mon"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="coding-tue"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="coding-wed"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="coding-thu"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="coding-fri"></td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">2:00–3:00 PM</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Technical Subject Study (OS / DBMS / OOPs)</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="technical-mon"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="technical-tue"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="technical-wed"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="technical-thu"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="technical-fri"></td>
                                </tr>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">3:00–4:00 PM</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">MCQs / Mock Tests / SQL Practice</td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="mcq-mon"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="mcq-tue"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="mcq-wed"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="mcq-thu"></td>
                                    <td class="px-6 py-4 whitespace-nowrap"><input type="checkbox" class="task-checkbox" data-task="mcq-fri"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Day Completion Toggles -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Mark Day Completed</h3>
                    <div class="grid grid-cols-2 sm:grid-cols-5 gap-4">
                        <div class="flex items-center">
                            <input type="checkbox" id="day-mon" class="day-checkbox mr-2" data-day="monday">
                            <label for="day-mon" class="text-sm font-medium text-gray-700">Monday</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="day-tue" class="day-checkbox mr-2" data-day="tuesday">
                            <label for="day-tue" class="text-sm font-medium text-gray-700">Tuesday</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="day-wed" class="day-checkbox mr-2" data-day="wednesday">
                            <label for="day-wed" class="text-sm font-medium text-gray-700">Wednesday</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="day-thu" class="day-checkbox mr-2" data-day="thursday">
                            <label for="day-thu" class="text-sm font-medium text-gray-700">Thursday</label>
                        </div>
                        <div class="flex items-center">
                            <input type="checkbox" id="day-fri" class="day-checkbox mr-2" data-day="friday">
                            <label for="day-fri" class="text-sm font-medium text-gray-700">Friday</label>
                        </div>
                    </div>
                </div>
                <!-- Weekend Schedule -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-blue-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Saturday</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input type="checkbox" class="task-checkbox mr-3" data-task="mock-interview-sat">
                                <span class="text-sm text-gray-700">Mock interviews</span>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="task-checkbox mr-3" data-task="resume-update-sat">
                                <span class="text-sm text-gray-700">Resume updates</span>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="task-checkbox mr-3" data-task="project-update-sat">
                                <span class="text-sm text-gray-700">Project updates</span>
                            </div>
                            <div class="flex items-center mt-4">
                                <input type="checkbox" id="day-sat" class="day-checkbox mr-2" data-day="saturday">
                                <label for="day-sat" class="text-sm font-medium text-gray-700">Mark Saturday Complete</label>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Sunday</h3>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input type="checkbox" class="task-checkbox mr-3" data-task="revision-sun">
                                <span class="text-sm text-gray-700">Revision</span>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" class="task-checkbox mr-3" data-task="light-practice-sun">
                                <span class="text-sm text-gray-700">Light practice</span>
                            </div>
                            <div class="flex items-center mt-4">
                                <input type="checkbox" id="day-sun" class="day-checkbox mr-2" data-day="sunday">
                                <label for="day-sun" class="text-sm font-medium text-gray-700">Mark Sunday Complete</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reset Button -->
                <div class="mt-8 text-center">
                    <button id="reset-timetable" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md">
                        Reset This Week
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 overflow-hidden sm:px-6 lg:px-8">
            <p class="mt-4 text-center text-base text-gray-500">
                &copy; 2025 Placement Prep Tracker. All rights reserved.
            </p>
        </div>
    </footer>

    <script src="js/app.js"></script>
    <script>
        // Timetable page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            loadTimetableData();
            setupTimetableEventListeners();
        });

        function setupTimetableEventListeners() {
            // Task checkboxes
            document.querySelectorAll('.task-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', saveTimetableData);
            });

            // Day completion checkboxes
            document.querySelectorAll('.day-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', saveTimetableData);
            });

            // Reset button
            document.getElementById('reset-timetable').addEventListener('click', resetTimetable);
        }

        function loadTimetableData() {
            const userData = PlacementPrepTracker.loadUserData();
            const timetableData = userData.timetable || {};

            // Load task checkboxes
            document.querySelectorAll('.task-checkbox').forEach(checkbox => {
                const taskId = checkbox.dataset.task;
                checkbox.checked = timetableData[taskId] || false;
            });

            // Load day completion checkboxes
            document.querySelectorAll('.day-checkbox').forEach(checkbox => {
                const day = checkbox.dataset.day;
                checkbox.checked = timetableData[`day-${day}`] || false;
            });
        }

        function saveTimetableData() {
            const userData = PlacementPrepTracker.loadUserData();
            if (!userData.timetable) userData.timetable = {};

            // Save task checkboxes
            document.querySelectorAll('.task-checkbox').forEach(checkbox => {
                const taskId = checkbox.dataset.task;
                userData.timetable[taskId] = checkbox.checked;
            });

            // Save day completion checkboxes
            document.querySelectorAll('.day-checkbox').forEach(checkbox => {
                const day = checkbox.dataset.day;
                userData.timetable[`day-${day}`] = checkbox.checked;
            });

            PlacementPrepTracker.saveUserData(userData);
        }

        function resetTimetable() {
            if (confirm('Are you sure you want to reset all timetable data for this week?')) {
                const userData = PlacementPrepTracker.loadUserData();
                userData.timetable = {};
                PlacementPrepTracker.saveUserData(userData);
                loadTimetableData();
                alert('Timetable has been reset.');
            }
        }
    </script>
</body>
</html>