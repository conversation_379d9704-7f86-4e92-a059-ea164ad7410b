<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technical - Placement Prep Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="index.html" class="text-xl font-semibold text-gray-800">Placement Prep Tracker</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Home
                        </a>
                        <a href="timetable.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Time Table
                        </a>
                        <a href="aptitude.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Aptitude
                        </a>
                        <a href="coding.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Coding
                        </a>
                        <a href="technical.html" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Technical
                        </a>
                        <a href="sql.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            SQL
                        </a>
                        <a href="mocks.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Mock Tests
                        </a>
                        <a href="resume.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Resume & Projects
                        </a>
                        <a href="profile.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Profile
                        </a>
                    </div>
                </div>
                <div class="sm:hidden">
                    <!-- Mobile menu button -->
                    <button type="button" class="bg-white inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500" aria-controls="mobile-menu" aria-expanded="false" id="mobile-menu-button">
                        <span class="sr-only">Open main menu</span>
                        <!-- Hamburger icon -->
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                        <!-- Close icon (hidden by default) -->
                        <svg class="hidden h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div class="sm:hidden hidden" id="mobile-menu">
            <div class="pt-2 pb-3 space-y-1">
                <a href="index.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Home</a>
                <a href="timetable.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Time Table</a>
                <a href="aptitude.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Aptitude</a>
                <a href="coding.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Coding</a>
                <a href="technical.html" class="bg-indigo-50 border-indigo-500 text-indigo-700 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Technical</a>
                <a href="sql.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">SQL</a>
                <a href="mocks.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Mock Tests</a>
                <a href="resume.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Resume & Projects</a>
                <a href="profile.html" class="border-transparent text-gray-600 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-800 block pl-3 pr-4 py-2 border-l-4 text-base font-medium">Profile</a>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white rounded-lg shadow px-5 py-6 sm:px-6">
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Technical Subjects</h1>
                    <p class="text-gray-600">Track your progress in OS, DBMS, and OOPs concepts</p>
                </div>

                <!-- Progress Overview -->
                <div class="mb-8 bg-yellow-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Overall Progress</h2>
                    <div class="w-full bg-gray-200 rounded-full h-4">
                        <div class="bg-yellow-600 h-4 rounded-full progress-bar" id="overall-progress" style="width: 0%"></div>
                    </div>
                    <div class="text-right text-sm text-gray-600 mt-2">
                        <span id="progress-text">0% complete</span>
                    </div>
                </div>

                <!-- Operating Systems -->
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">Operating Systems</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Processes</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="processes">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="processes"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Scheduling</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="scheduling">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="scheduling"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Deadlocks</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="deadlocks">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="deadlocks"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Paging</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="paging">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="paging"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Database Management Systems -->
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">Database Management Systems</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Normalization</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="normalization">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="normalization"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Joins</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="joins">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="joins"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Indexing</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="indexing">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="indexing"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Transactions</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="transactions">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="transactions"></textarea>
                        </div>
                    </div>
                </div>
                <!-- Object-Oriented Programming -->
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">Object-Oriented Programming</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Inheritance</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="inheritance">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="inheritance"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Polymorphism</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="polymorphism">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="polymorphism"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Abstraction</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="abstraction">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="abstraction"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Encapsulation</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="encapsulation">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="3" placeholder="Notes..." data-notes="encapsulation"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4">
                    <button id="save-progress" class="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-6 rounded-md">
                        Save Progress
                    </button>
                    <button id="reset-technical" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-6 rounded-md">
                        Reset All
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 overflow-hidden sm:px-6 lg:px-8">
            <p class="mt-4 text-center text-base text-gray-500">
                &copy; 2025 Placement Prep Tracker. All rights reserved.
            </p>
        </div>
    </footer>

    <script src="js/app.js"></script>
    <script>
        // Technical page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            loadTechnicalData();
            setupTechnicalEventListeners();
            updateProgress();
        });

        function setupTechnicalEventListeners() {
            // Topic checkboxes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    saveTechnicalData();
                    updateProgress();
                });
            });

            // Notes textareas
            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                textarea.addEventListener('blur', saveTechnicalData);
            });

            // Save button
            document.getElementById('save-progress').addEventListener('click', function() {
                saveTechnicalData();
                alert('Progress saved successfully!');
            });

            // Reset button
            document.getElementById('reset-technical').addEventListener('click', resetTechnicalData);
        }

        function loadTechnicalData() {
            const userData = PlacementPrepTracker.loadUserData();
            const technicalData = userData.technical || {};

            // Load checkboxes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                const topic = checkbox.dataset.topic;
                checkbox.checked = technicalData[topic]?.completed || false;
            });

            // Load notes
            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                const topic = textarea.dataset.notes;
                textarea.value = technicalData[topic]?.notes || '';
            });
        }

        function saveTechnicalData() {
            const userData = PlacementPrepTracker.loadUserData();
            if (!userData.technical) userData.technical = {};

            // Save checkboxes and notes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                const topic = checkbox.dataset.topic;
                if (!userData.technical[topic]) userData.technical[topic] = {};
                userData.technical[topic].completed = checkbox.checked;
            });

            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                const topic = textarea.dataset.notes;
                if (!userData.technical[topic]) userData.technical[topic] = {};
                userData.technical[topic].notes = textarea.value;
            });

            // Calculate and save overall progress
            const totalTopics = document.querySelectorAll('.topic-checkbox').length;
            const completedTopics = document.querySelectorAll('.topic-checkbox:checked').length;
            const progressPercentage = Math.round((completedTopics / totalTopics) * 100);

            userData.progress.technical = progressPercentage;

            PlacementPrepTracker.saveUserData(userData);
        }

        function updateProgress() {
            const totalTopics = document.querySelectorAll('.topic-checkbox').length;
            const completedTopics = document.querySelectorAll('.topic-checkbox:checked').length;
            const progressPercentage = Math.round((completedTopics / totalTopics) * 100);

            document.getElementById('overall-progress').style.width = progressPercentage + '%';
            document.getElementById('progress-text').textContent = progressPercentage + '% complete';
        }

        function resetTechnicalData() {
            if (confirm('Are you sure you want to reset all technical progress? This cannot be undone.')) {
                const userData = PlacementPrepTracker.loadUserData();
                userData.technical = {};
                userData.progress.technical = 0;
                PlacementPrepTracker.saveUserData(userData);
                loadTechnicalData();
                updateProgress();
                alert('Technical progress has been reset.');
            }
        }
    </script>
</body>
</html>