<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aptitude - Placement Prep Tracker</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body class="bg-gray-50">
    <nav class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex">
                    <div class="flex-shrink-0 flex items-center">
                        <a href="index.html" class="text-xl font-semibold text-gray-800">Placement Prep Tracker</a>
                    </div>
                    <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
                        <a href="index.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Home
                        </a>
                        <a href="timetable.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Time Table
                        </a>
                        <a href="aptitude.html" class="border-indigo-500 text-gray-900 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Aptitude
                        </a>
                        <a href="coding.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Coding
                        </a>
                        <a href="technical.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Technical
                        </a>
                        <a href="sql.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            SQL
                        </a>
                        <a href="mocks.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Mock Tests
                        </a>
                        <a href="resume.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Resume & Projects
                        </a>
                        <a href="profile.html" class="border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium">
                            Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <div class="bg-white rounded-lg shadow px-5 py-6 sm:px-6">
                <div class="text-center mb-8">
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">Aptitude Practice</h1>
                    <p class="text-gray-600">Track your progress in Quantitative Aptitude and Logical Reasoning</p>
                </div>

                <!-- Progress Overview -->
                <div class="mb-8 bg-blue-50 rounded-lg p-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Overall Progress</h2>
                    <div class="w-full bg-gray-200 rounded-full h-4">
                        <div class="bg-blue-600 h-4 rounded-full progress-bar" id="overall-progress" style="width: 0%"></div>
                    </div>
                    <div class="text-right text-sm text-gray-600 mt-2">
                        <span id="progress-text">0% complete</span>
                    </div>
                </div>

                <!-- Quantitative Aptitude Section -->
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">Quantitative Aptitude</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Number System</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="number-system">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="number-system"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Percentages</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="percentages">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="percentages"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Profit & Loss</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="profit-loss">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="profit-loss"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">SI/CI</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="si-ci">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="si-ci"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Time & Work</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="time-work">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="time-work"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Speed & Distance</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="speed-distance">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="speed-distance"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Ratios</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="ratios">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="ratios"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Averages</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="averages">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="averages"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Permutations</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="permutations">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="permutations"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Probability</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="probability">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="probability"></textarea>
                        </div>
                    </div>
                </div>
                <!-- Logical Reasoning Section -->
                <div class="mb-8">
                    <h2 class="text-2xl font-semibold text-gray-900 mb-6">Logical Reasoning</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Blood Relations</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="blood-relations">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="blood-relations"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Coding-Decoding</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="coding-decoding">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="coding-decoding"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Directions</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="directions">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="directions"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Syllogisms</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="syllogisms">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="syllogisms"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Puzzles</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="puzzles">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="puzzles"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Venn Diagrams</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="venn-diagrams">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="venn-diagrams"></textarea>
                        </div>

                        <div class="bg-white border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="text-lg font-medium text-gray-900">Seating</h3>
                                <input type="checkbox" class="topic-checkbox" data-topic="seating">
                            </div>
                            <textarea class="w-full text-sm border-gray-300 rounded-md" rows="2" placeholder="Notes..." data-notes="seating"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-center space-x-4">
                    <button id="save-progress" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md">
                        Save Progress
                    </button>
                    <button id="reset-aptitude" class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-6 rounded-md">
                        Reset All
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white mt-12">
        <div class="max-w-7xl mx-auto py-6 px-4 overflow-hidden sm:px-6 lg:px-8">
            <p class="mt-4 text-center text-base text-gray-500">
                &copy; 2025 Placement Prep Tracker. All rights reserved.
            </p>
        </div>
    </footer>

    <script src="js/app.js"></script>
    <script>
        // Aptitude page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            loadAptitudeData();
            setupAptitudeEventListeners();
            updateProgress();
        });

        function setupAptitudeEventListeners() {
            // Topic checkboxes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    saveAptitudeData();
                    updateProgress();
                });
            });

            // Notes textareas
            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                textarea.addEventListener('blur', saveAptitudeData);
            });

            // Save button
            document.getElementById('save-progress').addEventListener('click', function() {
                saveAptitudeData();
                alert('Progress saved successfully!');
            });

            // Reset button
            document.getElementById('reset-aptitude').addEventListener('click', resetAptitudeData);
        }

        function loadAptitudeData() {
            const userData = PlacementPrepTracker.loadUserData();
            const aptitudeData = userData.aptitude || {};

            // Load checkboxes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                const topic = checkbox.dataset.topic;
                checkbox.checked = aptitudeData[topic]?.completed || false;
            });

            // Load notes
            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                const topic = textarea.dataset.notes;
                textarea.value = aptitudeData[topic]?.notes || '';
            });
        }

        function saveAptitudeData() {
            const userData = PlacementPrepTracker.loadUserData();
            if (!userData.aptitude) userData.aptitude = {};

            // Save checkboxes and notes
            document.querySelectorAll('.topic-checkbox').forEach(checkbox => {
                const topic = checkbox.dataset.topic;
                if (!userData.aptitude[topic]) userData.aptitude[topic] = {};
                userData.aptitude[topic].completed = checkbox.checked;
            });

            document.querySelectorAll('textarea[data-notes]').forEach(textarea => {
                const topic = textarea.dataset.notes;
                if (!userData.aptitude[topic]) userData.aptitude[topic] = {};
                userData.aptitude[topic].notes = textarea.value;
            });

            // Calculate and save overall progress
            const totalTopics = document.querySelectorAll('.topic-checkbox').length;
            const completedTopics = document.querySelectorAll('.topic-checkbox:checked').length;
            const progressPercentage = Math.round((completedTopics / totalTopics) * 100);

            userData.progress.aptitude = progressPercentage;

            PlacementPrepTracker.saveUserData(userData);
        }

        function updateProgress() {
            const totalTopics = document.querySelectorAll('.topic-checkbox').length;
            const completedTopics = document.querySelectorAll('.topic-checkbox:checked').length;
            const progressPercentage = Math.round((completedTopics / totalTopics) * 100);

            document.getElementById('overall-progress').style.width = progressPercentage + '%';
            document.getElementById('progress-text').textContent = progressPercentage + '% complete';
        }

        function resetAptitudeData() {
            if (confirm('Are you sure you want to reset all aptitude progress? This cannot be undone.')) {
                const userData = PlacementPrepTracker.loadUserData();
                userData.aptitude = {};
                userData.progress.aptitude = 0;
                PlacementPrepTracker.saveUserData(userData);
                loadAptitudeData();
                updateProgress();
                alert('Aptitude progress has been reset.');
            }
        }
    </script>
</body>
</html>