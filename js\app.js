// Main application script for Placement Prep Tracker

// DOM Elements
const daysRemainingEl = document.getElementById('days-remaining');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
function initializeApp() {
    // Load saved data from localStorage
    loadUserData();
    
    // Set up event listeners
    setupEventListeners();
    
    // Update UI based on saved data
    updateUI();
}

// Set up event listeners
function setupEventListeners() {
    // Add event listeners for navigation
    document.querySelectorAll('nav a').forEach(link => {
        link.addEventListener('click', function(e) {
            // Handle navigation (for SPA-like behavior if needed)
            // For now, let the default link behavior work
        });
    });
    
    // Add event listener for mobile menu toggle if it exists
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    if (mobileMenuButton) {
        mobileMenuButton.addEventListener('click', toggleMobileMenu);
    }
}

// Toggle mobile menu
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    if (mobileMenu) {
        mobileMenu.classList.toggle('hidden');
    }
}

// Load user data from localStorage
function loadUserData() {
    // Check if user data exists in localStorage
    const userData = JSON.parse(localStorage.getItem('placementPrepUserData')) || {
        profile: {
            name: '',
            email: '',
            placementStartDate: '',
            targetCompanies: []
        },
        progress: {
            aptitude: 0,
            coding: 0,
            technical: 0,
            mocksCompleted: 0
        },
        lastUpdated: new Date().toISOString()
    };
    
    return userData;
}

// Save user data to localStorage
function saveUserData(userData) {
    userData.lastUpdated = new Date().toISOString();
    localStorage.setItem('placementPrepUserData', JSON.stringify(userData));
    updateUI();
}

// Calculate days remaining until placement
function calculateDaysRemaining() {
    const userData = loadUserData();
    if (!userData.profile.placementStartDate) return null;
    
    const today = new Date();
    const placementDate = new Date(userData.profile.placementStartDate);
    
    // Reset time part to avoid timezone issues
    today.setHours(0, 0, 0, 0);
    placementDate.setHours(0, 0, 0, 0);
    
    // Calculate difference in days
    const diffTime = placementDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return diffDays > 0 ? diffDays : 0;
}

// Update UI based on current data
function updateUI() {
    updateDaysRemaining();
    updateProgressBars();
}

// Update days remaining display
function updateDaysRemaining() {
    if (!daysRemainingEl) return;
    
    const daysRemaining = calculateDaysRemaining();
    
    if (daysRemaining !== null) {
        daysRemainingEl.textContent = `${daysRemaining} days left until placements!`;
        daysRemainingEl.classList.remove('text-blue-700');
        daysRemainingEl.classList.add('font-semibold', 'text-green-600');
    } else {
        daysRemainingEl.textContent = 'Set your placement start date in the Profile section to see days remaining.';
        daysRemainingEl.classList.remove('font-semibold', 'text-green-600');
        daysRemainingEl.classList.add('text-blue-700');
    }
}

// Update progress bars based on user progress
function updateProgressBars() {
    const userData = loadUserData();

    // Calculate actual progress from different sections
    const progressData = {
        aptitude: calculateAptitudeProgress(userData),
        coding: calculateCodingProgress(userData),
        technical: calculateTechnicalProgress(userData)
    };

    // Update progress bars
    updateProgressBar('aptitude', progressData.aptitude);
    updateProgressBar('coding', progressData.coding);
    updateProgressBar('technical', progressData.technical);

    // Update mocks count
    const mocksCountEl = document.querySelector('[data-mocks-count]');
    if (mocksCountEl) {
        const mocksCount = userData.mocks ? userData.mocks.length : 0;
        mocksCountEl.textContent = mocksCount;
    }
}

// Update a single progress bar
function updateProgressBar(type, percentage) {
    const progressBar = document.querySelector(`.progress-${type}`);
    const percentageEl = document.querySelector(`.percentage-${type}`);
    
    if (progressBar) {
        progressBar.style.width = `${percentage}%`;
    }
    
    if (percentageEl) {
        percentageEl.textContent = `${percentage}%`;
    }
}

// Calculate aptitude progress
function calculateAptitudeProgress(userData) {
    if (!userData.aptitude) return 0;

    const aptitudeTopics = [
        'number-system', 'percentages', 'profit-loss', 'si-ci', 'time-work',
        'speed-distance', 'ratios', 'averages', 'permutations', 'probability',
        'blood-relations', 'coding-decoding', 'directions', 'syllogisms',
        'puzzles', 'venn-diagrams', 'seating'
    ];

    const completedTopics = aptitudeTopics.filter(topic =>
        userData.aptitude[topic]?.completed
    ).length;

    return Math.round((completedTopics / aptitudeTopics.length) * 100);
}

// Calculate coding progress
function calculateCodingProgress(userData) {
    if (!userData.coding) return 0;

    const codingTopics = [
        'arrays', 'strings', 'sorting', 'hashing', 'stack', 'queue',
        'linkedlist', 'recursion', 'trees', 'binary-search',
        'sliding-window', 'graphs', 'dp'
    ];

    const completedTopics = codingTopics.filter(topic =>
        userData.coding[topic]?.completed
    ).length;

    return Math.round((completedTopics / codingTopics.length) * 100);
}

// Calculate technical progress
function calculateTechnicalProgress(userData) {
    if (!userData.technical) return 0;

    const technicalTopics = [
        'processes', 'scheduling', 'deadlocks', 'paging',
        'normalization', 'joins', 'indexing', 'transactions',
        'inheritance', 'polymorphism', 'abstraction', 'encapsulation'
    ];

    const completedTopics = technicalTopics.filter(topic =>
        userData.technical[topic]?.completed
    ).length;

    return Math.round((completedTopics / technicalTopics.length) * 100);
}

// Format date to YYYY-MM-DD
function formatDate(date) {
    const d = new Date(date);
    let month = '' + (d.getMonth() + 1);
    let day = '' + d.getDate();
    const year = d.getFullYear();

    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;

    return [year, month, day].join('-');
}

// Export functions for use in other modules
window.PlacementPrepTracker = {
    loadUserData,
    saveUserData,
    calculateDaysRemaining,
    updateUI,
    formatDate,
    calculateAptitudeProgress,
    calculateCodingProgress,
    calculateTechnicalProgress
};
