/* Custom styles for Placement Prep Tracker */

/* Base styles */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.6;
    color: #1f2937;
}

/* Container for main content */
.main-container {
    min-height: calc(100vh - 120px);
}

/* Card styles */
.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Progress bar styles */
.progress-bar {
    transition: width 0.6s ease;
}

/* Mobile menu styles */
.mobile-menu {
    display: none;
}

@media (max-width: 640px) {
    .mobile-menu {
        display: block;
    }
    
    .desktop-menu {
        display: none;
    }
}

/* Form controls */
input[type="text"],
input[type="date"],
input[type="email"],
select,
textarea {
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    width: 100%;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

input[type="text"]:focus,
input[type="date"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Checkbox styling */
.checkbox-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.checkbox-container input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
}

.btn-outline {
    border: 1px solid #d1d5db;
    background-color: white;
    color: #374151;
}

.btn-outline:hover {
    background-color: #f9fafb;
    border-color: #9ca3af;
}

/* Table styles */
.table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th, td {
    padding: 0.75rem 1rem;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
}

th {
    background-color: #f9fafb;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    color: #6b7280;
}

tbody tr:hover {
    background-color: #f9fafb;
}

/* Responsive typography */
@media (max-width: 640px) {
    h1 {
        font-size: 1.5rem;
    }
    
    h2 {
        font-size: 1.25rem;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    @page {
        margin: 1cm;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.3;
    }
}
